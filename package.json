{"name": "latex-ocr-electron", "version": "4.3.1", "description": "TexStudio OCR", "author": "TexStudio", "main": "dist/electron/electron/main.js", "scripts": {"start": "electron . --expose-gc --max-old-space-size=512", "dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:3000 && npm run dev:electron\"", "dev:react": "react-scripts start", "dev:electron": "cross-env NODE_ENV=development electron . --expose-gc --max-old-space-size=512", "build": "npm run build:react && npm run build:electron", "build:react": "react-scripts build", "build:electron": "tsc -p tsconfig.electron.json", "package": "npm run build && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder", "dist": "npm run build && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --publish=never", "publish": "npm run build && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --publish=always"}, "dependencies": {"@types/crypto-js": "^4.2.2", "@types/katex": "^0.16.7", "axios": "^1.5.0", "crypto-js": "^4.2.0", "docx": "^9.5.1", "docx-templates": "^4.14.1", "electron-store": "^8.1.0", "electron-updater": "^6.6.2", "form-data": "^4.0.0", "katex": "^0.16.22", "mammoth": "^1.9.1", "mathjax-node": "^2.1.1", "office-document-properties": "^1.1.0", "officegen": "^0.6.5", "openai": "^5.8.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "sharp": "^0.34.2", "styled-components": "^6.0.0"}, "devDependencies": {"@types/form-data": "^2.5.0", "@types/mathjax-node": "^2.1.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-katex": "^3.0.4", "@types/styled-components": "^5.1.26", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "electron": "^27.0.0", "electron-builder": "^24.6.0", "react-scripts": "^5.0.1", "typescript": "^4.9.5", "wait-on": "^7.0.1"}, "homepage": "./", "build": {"appId": "com.latex-ocr.app", "productName": "TexStudio", "directories": {"output": "release"}, "files": ["build/**/*", "dist/**/*", "node_modules/**/*", "settings.json", "!assets/**/*"], "extraMetadata": {"main": "dist/electron/electron/main.js"}, "win": {"icon": null, "target": "nsis", "artifactName": "TexStudio-${version}.exe"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "TexStudio", "deleteAppDataOnUninstall": true}, "publish": {"provider": "github", "owner": "Louaq", "repo": "SimpleTex-OCR", "private": false, "releaseType": "release", "vPrefixedTagName": true}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}